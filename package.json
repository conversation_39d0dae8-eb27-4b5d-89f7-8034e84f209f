{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "husky": "^9", "lint-staged": "^15", "prettier": "^3.6.2", "tailwindcss": "^4", "typescript": "^5"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx,mjs,ts,tsx}": ["prettier --write", "eslint --fix"], "src/**/*.{css,scss,less,json,html,md,markdown}": ["prettier --write"]}}