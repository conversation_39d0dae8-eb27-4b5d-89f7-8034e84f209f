{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "19.1.0", "react-dom": "19.1.0", "next": "15.4.5"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.5", "@eslint/eslintrc": "^3", "husky": "^9", "lint-staged": "^15"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/*.{js,jsx,mjs,ts,tsx}": ["node_modules/.bin/prettier --write", "node_modules/.bin/eslint --fix", "git add"], "src/*.{css,scss,less,json,html,md,markdown}": ["node_modules/.bin/prettier --write", "git add"]}}